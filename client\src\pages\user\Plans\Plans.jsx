import React, { useEffect, useState } from "react";
import { getPlans } from "../../../apicalls/plans";
import "./Plans.css";
import ConfirmModal from "./components/ConfirmModal";
import WaitingModal from "./components/WaitingModal";
import { addPayment } from "../../../apicalls/payment";
import { useDispatch, useSelector } from "react-redux";
import { setPaymentVerificationNeeded } from "../../../redux/paymentSlice";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { message } from "antd";
import { useNavigate } from "react-router-dom";

const Plans = () => {
    const [plans, setPlans] = useState([]);
    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);
    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);
    const [paymentInProgress, setPaymentInProgress] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const { user } = useSelector((state) => state.user);
    const { subscriptionData } = useSelector((state) => state.subscription);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    // Validate if user has a valid phone number for payment
    const validatePhoneForPayment = () => {
        if (!user?.phoneNumber || user.phoneNumber.trim() === "") {
            return {
                valid: false,
                message: "Phone number is required for payment confirmation. Please update your profile."
            };
        }

        // Tanzania phone number format validation
        const phoneRegex = /^0[67]\d{8}$/;
        if (!phoneRegex.test(user.phoneNumber)) {
            return {
                valid: false,
                message: "Please update your phone number to a valid Tanzania format (06xxxxxxxx or 07xxxxxxxx)."
            };
        }

        return { valid: true };
    };

    useEffect(() => {
        const fetchPlans = async () => {
            try {
                const response = await getPlans();
                setPlans(response);
            } catch (error) {
                console.error("Error fetching plans:", error);
            }
        };

        fetchPlans();
    }, []);

    const transactionDetails = {
        amount: selectedPlan?.discountedPrice || 'N/A',
        currency: "TZS",
        destination: "brainwave.zone",
    };


    const handlePaymentStart = async (plan) => {
        setSelectedPlan(plan);

        // Validate phone number before proceeding
        const phoneValidation = validatePhoneForPayment();
        if (!phoneValidation.valid) {
            message.error({
                content: phoneValidation.message,
                duration: 5,
                onClick: () => navigate('/user/profile') // Allow user to click message to go to profile
            });
            return;
        }

        try {
            dispatch(ShowLoading());
            console.log('💳 Initiating payment for plan:', plan.title);
            console.log('📱 User phone number:', user.phoneNumber);

            const response = await addPayment({ plan });
            console.log('📥 Payment response:', response);

            if (response.success) {
                localStorage.setItem("order_id", response.order_id);
                setWaitingModalOpen(true);
                setPaymentInProgress(true);
                dispatch(setPaymentVerificationNeeded(true));

                // Show success message with phone number confirmation
                message.success({
                    content: response.message || `Payment request sent! Please check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,
                    duration: 8
                });
            } else {
                // Handle specific error types
                if (response.errorType === "MISSING_PHONE") {
                    message.error({
                        content: "Please add a phone number to your profile before making a payment.",
                        duration: 5,
                        onClick: () => navigate('/user/profile')
                    });
                } else if (response.errorType === "INVALID_PHONE_FORMAT") {
                    message.error({
                        content: "Please update your phone number to a valid Tanzania format (06xxxxxxxx or 07xxxxxxxx).",
                        duration: 5,
                        onClick: () => navigate('/user/profile')
                    });
                } else if (response.errorType === "PAYMENT_CONFIG_ERROR") {
                    message.error("Payment service is temporarily unavailable. Please contact support.");
                } else {
                    message.error(response.message || "Payment initiation failed. Please try again.");
                }
            }
        } catch (error) {
            console.error("❌ Error processing payment:", error);

            // Handle network or other errors
            if (error.response?.data?.message) {
                message.error(error.response.data.message);
            } else if (error.message) {
                message.error(`Payment error: ${error.message}`);
            } else {
                message.error("Unable to process payment. Please check your internet connection and try again.");
            }
        } finally {
            dispatch(HideLoading());
        }
    };


    useEffect(() => {
        console.log("subscription Data in Plans", subscriptionData)
        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === "paid" && paymentInProgress) {
            setWaitingModalOpen(false);
            setConfirmModalOpen(true);
            setPaymentInProgress(false);
        }
    }, [user, subscriptionData]);

    return (
        <div>
            {!user ?
                <>
                </>
                :
                !user.paymentRequired ?
                    <div className="no-plan-required">
                        <div className="no-plan-content">
                            <h2>No Plan Required</h2>
                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>
                        </div>
                    </div>
                    :
                    subscriptionData?.paymentStatus !== "paid" ?
                        <div className="plans-container">
                            {plans
                                .sort((a, b) => {
                                    // Sort order: Glimp Plan first, then Basic Membership, then others
                                    if (a.title === "Glimp Plan") return -1;
                                    if (b.title === "Glimp Plan") return 1;
                                    if (a.title === "Basic Membership") return -1;
                                    if (b.title === "Basic Membership") return 1;
                                    return 0;
                                })
                                .map((plan) => (
                                <div
                                    key={plan._id}
                                    className={`plan-card ${
                                        plan.title === "Basic Membership" ? "basic" :
                                        plan.title === "Glimp Plan" ? "glimp" : ""
                                    }`}
                                >
                                    {plan.title === "Basic Membership" && (
                                        <div className="most-popular-label">MOST POPULAR</div>
                                    )}
                                    {plan.title === "Glimp Plan" && (
                                        <div className="glimp-label">QUICK START</div>
                                    )}

                                    <div className="plan-header">
                                        <h2 className="plan-title">{plan.title}</h2>
                                        <div className="plan-duration-highlight">
                                            <span className="duration-number">{plan.duration}</span>
                                            <span className="duration-text">Month{plan.duration > 1 ? 's' : ''}</span>
                                        </div>
                                    </div>

                                    <div className="plan-pricing">
                                        <p className="plan-actual-price">
                                            {plan.actualPrice.toLocaleString()} TZS
                                        </p>
                                        <p className="plan-discounted-price">
                                            {plan.discountedPrice.toLocaleString()} TZS
                                        </p>
                                        <span className="plan-discount-tag">
                                            {plan.discountPercentage}% OFF
                                        </span>
                                    </div>

                                    <div className="plan-value">
                                        <span className="value-text">
                                            {Math.round(plan.discountedPrice / plan.duration).toLocaleString()} TZS/month
                                        </span>
                                    </div>

                                    <button className="plan-button"
                                        onClick={() => handlePaymentStart(plan)}
                                    >
                                        {plan.title === "Glimp Plan" ? "🚀 Start Quick" : "Choose Plan"}
                                    </button>

                                    <ul className="plan-features">
                                        {plan.features.map((feature, index) => (
                                            <li key={index} className="plan-feature">
                                                <span className="plan-feature-icon">✔</span>
                                                {feature}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            ))}
                        </div>
                        :
                        <div className="subscription-details">
                            <h1 className="plan-title">{subscriptionData.plan.title}</h1>

                            <svg
                                width="64px"
                                height="64px"
                                viewBox="-3.2 -3.2 38.40 38.40"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="#10B981"
                                stroke="#253864"
                                transform="matrix(1, 0, 0, 1, 0, 0)"
                            >
                                <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
                                <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" stroke="#CCCCCC" strokeWidth="0.064"></g>
                                <g id="SVGRepo_iconCarrier">
                                    <path
                                        d="m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z"
                                        fill="#202327"
                                        fillRule="evenodd"
                                    ></path>
                                </g>
                            </svg>

                            <p className="plan-description">{subscriptionData?.plan?.subscriptionData}</p>
                            <p className="plan-dates">Start Date: {subscriptionData.startDate}</p>
                            <p className="plan-dates">End Date: {subscriptionData.endDate}</p>
                        </div>
            }

            <WaitingModal
                isOpen={isWaitingModalOpen}
                onClose={() => setWaitingModalOpen(false)}
            />

            <ConfirmModal
                isOpen={isConfirmModalOpen}
                onClose={() => setConfirmModalOpen(false)}
                transaction={transactionDetails}
            />
        </div>
    );
};

export default Plans;
