import React, { useEffect, useState } from "react";
import Modal from "react-modal";
import "./WaitingModal.css";

Modal.setAppElement("#root"); // Ensure accessibility for screen readers

const WaitingModal = ({ isOpen, onClose }) => {
    const [dots, setDots] = useState('');

    // Animated dots for loading effect
    useEffect(() => {
        const interval = setInterval(() => {
            setDots(prev => prev.length >= 3 ? '' : prev + '.');
        }, 500);
        return () => clearInterval(interval);
    }, []);

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            className="waiting-modal-content"
            overlayClassName="waiting-modal-overlay"
        >
            <div className="waiting-modal-header">
                <div className="payment-icon-container">
                    <div className="payment-processing-icon">
                        <svg
                            width="80px"
                            height="80px"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="payment-icon"
                        >
                            <circle cx="12" cy="12" r="10" stroke="#007BFF" strokeWidth="2" fill="none"/>
                            <path d="M8 12l2 2 4-4" stroke="#007BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <div className="loading-spinner"></div>
                    </div>
                </div>
                <h2>Processing Payment{dots}</h2>
                <p className="payment-subtitle">Please wait while we process your payment securely</p>
            </div>

            <div className="waiting-modal-timer">
                <div className="progress-container">
                    <div className="progress-bar">
                        <div className="progress-fill"></div>
                    </div>
                    <p className="progress-text">Connecting to secure payment gateway...</p>
                </div>
            </div>


            <div className="payment-instructions">
                <div className="instruction-item">
                    <span className="step-number">1</span>
                    <span className="step-text">Check your phone for SMS confirmation</span>
                </div>
                <div className="instruction-item">
                    <span className="step-number">2</span>
                    <span className="step-text">Follow the instructions in the message</span>
                </div>
                <div className="instruction-item">
                    <span className="step-number">3</span>
                    <span className="step-text">Complete the payment process</span>
                </div>
            </div>

            <div className="security-notice">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" stroke="#10B981" strokeWidth="2" fill="none"/>
                    <path d="M9 12L11 14L15 10" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>Your payment is secured with bank-level encryption</span>
            </div>
        </Modal>
    );
};

export default WaitingModal;
