import React, { useEffect, useState } from "react";
import Modal from "react-modal";
import "./WaitingModal.css";

Modal.setAppElement("#root"); // Ensure accessibility for screen readers

const WaitingModal = ({ isOpen, onClose }) => {
    const [dots, setDots] = useState('');

    // Animated dots for loading effect
    useEffect(() => {
        const interval = setInterval(() => {
            setDots(prev => prev.length >= 3 ? '' : prev + '.');
        }, 500);
        return () => clearInterval(interval);
    }, []);

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            className="waiting-modal-content"
            overlayClassName="waiting-modal-overlay"
        >
            <div className="waiting-modal-header">
                <div className="payment-icon-container">
                    <div className="payment-processing-icon">
                        <svg
                            width="80px"
                            height="80px"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="payment-icon"
                        >
                            <circle cx="12" cy="12" r="10" stroke="#007BFF" strokeWidth="2" fill="none"/>
                            <path d="M8 12l2 2 4-4" stroke="#007BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <div className="loading-spinner"></div>
                    </div>
                </div>
                <h2>Processing Payment{dots}</h2>
                <p className="payment-subtitle">Please wait while we process your payment securely</p>
            </div>

            <div className="waiting-modal-timer">
                <div className="progress-container">
                    <div className="progress-bar">
                        <div className="progress-fill"></div>
                    </div>
                    <p className="progress-text">Connecting to secure payment gateway...</p>
                </div>
                        <path d="M437.019,74.981C388.668,26.629,324.38,0,256,0S123.332,26.629,74.981,74.981C26.629,123.332,0,187.62,0,256 s26.629,132.668,74.981,181.019C123.332,485.371,187.62,512,256,512c64.518,0,126.15-24.077,173.541-67.796l-10.312-11.178 c-44.574,41.12-102.544,63.766-163.229,63.766c-64.317,0-124.786-25.046-170.266-70.527 C40.254,380.786,15.208,320.317,15.208,256S40.254,131.214,85.734,85.735C131.214,40.254,191.683,15.208,256,15.208 s124.786,25.046,170.266,70.527c45.48,45.479,70.526,105.948,70.526,170.265c0,60.594-22.587,118.498-63.599,163.045 l11.188,10.301C487.986,381.983,512,320.421,512,256C512,187.62,485.371,123.332,437.019,74.981z"></path>
                        <path d="M282.819,263.604h63.415v-15.208h-63.415c-1.619-5.701-5.007-10.662-9.536-14.25l35.913-86.701l-14.049-5.82 l-35.908,86.688c-1.064-0.124-2.142-0.194-3.238-0.194c-15.374,0-27.881,12.508-27.881,27.881s12.507,27.881,27.881,27.881 C268.737,283.881,279.499,275.292,282.819,263.604z M243.327,256c0-6.989,5.685-12.673,12.673-12.673 c6.989,0,12.673,5.685,12.673,12.673c0,6.989-5.685,12.673-12.673,12.673C249.011,268.673,243.327,262.989,243.327,256z"></path>
                        <path d="M451.168,256c0-107.616-87.552-195.168-195.168-195.168S60.832,148.384,60.832,256S148.384,451.168,256,451.168 S451.168,363.616,451.168,256z M76.04,256c0-99.231,80.73-179.96,179.96-179.96S435.96,156.769,435.96,256 S355.231,435.96,256,435.96S76.04,355.231,76.04,256z"></path>
                    </g>
                </svg>
            </div>


            <div className="payment-instructions">
                <div className="instruction-item">
                    <span className="step-number">1</span>
                    <span className="step-text">Check your phone for SMS confirmation</span>
                </div>
                <div className="instruction-item">
                    <span className="step-number">2</span>
                    <span className="step-text">Follow the instructions in the message</span>
                </div>
                <div className="instruction-item">
                    <span className="step-number">3</span>
                    <span className="step-text">Complete the payment process</span>
                </div>
            </div>

            <div className="security-notice">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" stroke="#10B981" strokeWidth="2" fill="none"/>
                    <path d="M9 12L11 14L15 10" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>Your payment is secured with bank-level encryption</span>
            </div>
        </Modal>
    );
};

export default WaitingModal;
