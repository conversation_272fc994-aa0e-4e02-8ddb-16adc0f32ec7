
/* Modal Overlay */
.modal-overlay {
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Modal Content */
.modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 32px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.success-modal {
    border: 2px solid #10B981;
}

/* Confetti Animation */
.confetti-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.confetti {
    position: absolute;
    width: 8px;
    height: 8px;
    animation: confetti-fall 3s linear forwards;
}

.confetti-0 { background: #ff6b6b; left: 10%; animation-delay: 0s; }
.confetti-1 { background: #4ecdc4; left: 30%; animation-delay: 0.2s; }
.confetti-2 { background: #45b7d1; left: 50%; animation-delay: 0.4s; }
.confetti-3 { background: #f9ca24; left: 70%; animation-delay: 0.6s; }
.confetti-4 { background: #6c5ce7; left: 90%; animation-delay: 0.8s; }

/* Header Icon */
.modal-header {
    margin-bottom: 24px;
}

.success-icon-container {
    position: relative;
    display: inline-block;
    margin-bottom: 16px;
}

.success-checkmark {
    position: relative;
    z-index: 2;
}

.success-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: glow-pulse 2s ease-in-out infinite;
}

.check-circle {
    animation: scale-in 0.5s ease-out;
}

.check-mark {
    animation: draw-check 0.8s ease-out 0.3s both;
    stroke-dasharray: 12;
    stroke-dashoffset: 12;
}

/* Title and Subtitle */
.modal-title {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, #10B981, #059669);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 12px;
    animation: title-bounce 0.6s ease-out 0.5s both;
}

.modal-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 24px;
    font-weight: 500;
}

/* Success Features */
.success-features {
    background: #f0fdf4;
    border-radius: 12px;
    padding: 20px;
    margin: 24px 0;
    border: 1px solid #bbf7d0;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    text-align: left;
}

.feature-item:last-child {
    margin-bottom: 0;
}

.feature-icon {
    font-size: 1.5rem;
    margin-right: 12px;
    flex-shrink: 0;
}

.feature-text h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.feature-text p {
    font-size: 0.85rem;
    color: #6b7280;
    margin: 0;
}

/* Details Section */
.modal-details {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    text-align: left;
    border: 1px solid #e2e8f0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 0.95rem;
    color: #1e293b;
    font-weight: 500;
}

.success-status {
    color: #059669;
    font-weight: 600;
}

/* Button */
.success-button {
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    font-size: 1.1rem;
    font-weight: 700;
    padding: 16px 24px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
}

.success-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.success-button:active {
    transform: translateY(0);
}

/* Animations */
@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

@keyframes scale-in {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes draw-check {
    0% {
        stroke-dashoffset: 12;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes glow-pulse {
    0%, 100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes title-bounce {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    60% {
        transform: translateY(5px);
        opacity: 1;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .modal-content {
        margin: 20px;
        padding: 24px;
        max-width: calc(100vw - 40px);
    }

    .modal-title {
        font-size: 1.6rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        align-items: center;
    }

    .feature-icon {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}
