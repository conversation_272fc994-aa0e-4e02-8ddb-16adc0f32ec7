const router = require("express").Router();
const User = require("../models/userModel");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const authMiddleware = require("../middlewares/authMiddleware");
const inactivityPenaltyService = require("../services/inactivityPenaltyService");
const nodemailer = require("nodemailer");
const multer = require("multer");
const { Storage } = require("@google-cloud/storage");
const { v4: uuidv4 } = require("uuid");
const AWS = require("aws-sdk");
const Review = require("../models/reviewModel");
const Report = require("../models/reportModel");

const forumQuestion = require("../models/forumQuestionModel");
const mongoose = require("mongoose");

// Configure Multer Memory Storage
const storage = multer.memoryStorage();
const upload = multer({ storage });

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

// Create a Nodemailer transporter
const transporter = nodemailer.createTransport({
  service: "Gmail",
  auth: {
    user: process.env.SENDER_EMAIL,
    pass: process.env.SENDER_EMAIL_PASSWORD,
  },
});

// Google Cloud Storage Setup
const GoogleStorage = new Storage({
  projectId: "proud-stage-416018",
  keyFilename: "proud-stage-416018-d682ff695aac.json",
});



function generateOTP() {
  const otp = Math.floor(100000 + Math.random() * 900000);
  return otp.toString();
}

// const generateOTP = (length) => {
//   const digits = '0123456789';
//   let otp = '';

//   for (let i = 0; i < length; i++) {
//     const randomIndex = Math.floor(Math.random() * digits.length);
//     otp += digits[randomIndex];
//   }

//   return otp;
// }

// user registration

router.post("/register", async (req, res) => {
  try {
    console.log("📝 Registration request body:", req.body);
    // Check if username already exists
    const usernameExists = await User.findOne({ username: req.body.username });
    if (usernameExists) {
      return res.status(409).send({
        message: "Username is already taken",
        success: false,
        errorType: "USERNAME_EXISTS"
      });
    }

    // Check if phone number already exists
    const phoneExists = await User.findOne({ phoneNumber: req.body.phoneNumber });
    if (phoneExists) {
      return res.status(409).send({
        message: "Phone number is used by another person",
        success: false,
        errorType: "PHONE_EXISTS"
      });
    }

    // Validate phone number format (Tanzania format - must start with 06 or 07)
    const phoneRegex = /^0[67]\d{8}$/;
    if (!phoneRegex.test(req.body.phoneNumber)) {
      return res.status(400).send({
        message: "Phone number is wrong written",
        success: false,
        errorType: "INVALID_PHONE"
      });
    }

    // Validate username format
    if (!req.body.username || req.body.username.length < 3) {
      return res.status(400).send({
        message: "Username must be at least 3 characters long",
        success: false,
        errorType: "INVALID_USERNAME"
      });
    }

    // hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(req.body.password, salt);

    // Create combined name for backward compatibility
    const fullName = `${req.body.firstName} ${req.body.middleName || ''} ${req.body.lastName}`.replace(/\s+/g, ' ').trim();

    // Prepare user data
    const userData = {
      firstName: req.body.firstName,
      middleName: req.body.middleName,
      lastName: req.body.lastName,
      username: req.body.username,
      name: fullName, // Combined name for backward compatibility
      school: req.body.school,
      level: req.body.level,
      class: req.body.class,
      phoneNumber: req.body.phoneNumber,
      password: hashedPassword,
      paymentRequired: true
    };

    // create new user
    console.log("👤 Creating user with data:", userData);
    const newUser = new User(userData);
    await newUser.save();
    console.log("✅ User created successfully with username:", newUser.username);
    res.send({
      message: "🎉 Account created successfully! Welcome to BrainWave!",
      success: true,
    });
  } catch (error) {
    console.error("Registration error:", error);

    // Handle specific MongoDB errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      let message = "";

      if (field === "username") {
        message = "Username is already taken";
      } else if (field === "phoneNumber") {
        message = "Phone number is used by another person";
      } else {
        message = "This information is already registered. Please try with different details.";
      }

      return res.status(409).send({
        message,
        success: false,
        errorType: "DUPLICATE_ENTRY"
      });
    }

    res.status(500).send({
      message: "Something went wrong while creating your account. Please try again in a few moments.",
      success: false,
      errorType: "SERVER_ERROR"
    });
  }
});



// Send OTP for verification

router.post("/generate-otp", async (req, res) => {
  try {
    const { email, phoneNumber, name } = req.body;

    // Validate required fields
    if (!email || !phoneNumber || !name) {
      return res.status(400).send({
        message: "Please fill in all required fields (name, email, and phone number)",
        success: false,
        errorType: "MISSING_FIELDS"
      });
    }

    // Validate email format
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      return res.status(400).send({
        message: "Please enter a valid email address (e.g., <EMAIL>)",
        success: false,
        errorType: "INVALID_EMAIL"
      });
    }

    // Check if email already exists
    const emailExists = await User.findOne({ email });
    if (emailExists) {
      return res.status(409).send({
        message: "Email is used by another person",
        success: false,
        errorType: "EMAIL_EXISTS"
      });
    }

    // Validate phone number format (Tanzania format - must start with 06 or 07)
    const phoneRegex = /^0[67]\d{8}$/;
    if (!phoneRegex.test(phoneNumber)) {
      return res.status(400).send({
        message: "Phone number is wrong written",
        success: false,
        errorType: "INVALID_PHONE"
      });
    }

    // Check if phone number already exists
    const phoneExists = await User.findOne({ phoneNumber });
    if (phoneExists) {
      return res.status(409).send({
        message: "Phone number is used by another person",
        success: false,
        errorType: "PHONE_EXISTS"
      });
    }

    const randomOTP = generateOTP();
    const mailOptions = {
      from: process.env.SENDER_EMAIL,
      to: email,
      subject: "🔐 BrainWave - Verify Your Email Address",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 20px;">
            <h1 style="color: white; margin: 0; font-size: 24px;">Welcome to BrainWave! 🎉</h1>
          </div>

          <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${name}! 👋</h2>

            <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
              Thank you for joining BrainWave! To complete your registration, please verify your email address using the code below:
            </p>

            <div style="background: #f0f9ff; border: 2px solid #0ea5e9; border-radius: 8px; padding: 20px; text-align: center; margin: 25px 0;">
              <p style="color: #0369a1; font-size: 14px; margin-bottom: 10px; font-weight: bold;">Your Verification Code:</p>
              <div style="font-size: 32px; font-weight: bold; color: #0c4a6e; letter-spacing: 5px; font-family: monospace;">
                ${randomOTP}
              </div>
            </div>

            <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0;">
              <p style="color: #92400e; margin: 0; font-size: 14px;">
                <strong>⏰ Important:</strong> This code will expire in 10 minutes for security reasons.
              </p>
            </div>

            <p style="color: #666; font-size: 14px; line-height: 1.6;">
              If you didn't request this verification, please ignore this email. Your account will not be created without verification.
            </p>

            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 25px 0;">

            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
              © 2024 BrainWave - Study Smarter | Kigamboni, Dar es Salaam, Tanzania
            </p>
          </div>
        </div>
      `
    };

    // Debug: Log email configuration
    console.log("📧 Email Configuration Check:");
    console.log("SENDER_EMAIL:", process.env.SENDER_EMAIL ? "✅ Set" : "❌ Missing");
    console.log("SENDER_EMAIL_PASSWORD:", process.env.SENDER_EMAIL_PASSWORD ? "✅ Set" : "❌ Missing");
    console.log("Sending email to:", email);

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.log("❌ Error sending email:", error);
        console.log("Error details:", {
          code: error.code,
          command: error.command,
          response: error.response,
          responseCode: error.responseCode
        });
        return res.status(500).send({
          message: "Failed to send verification code",
          success: false,
          errorType: "EMAIL_SEND_FAILED",
          details: error.message
        });
      }
      console.log("✅ Email sent successfully:", info.response);
      res.send({
        message: `📧 Verification code sent to ${email}! Please check your inbox (and spam folder).`,
        success: true,
        data: randomOTP,
      });
    });
  } catch (error) {
    console.error("OTP generation error:", error);
    res.status(500).send({
      message: "Something went wrong while sending the verification code. Please try again in a few moments.",
      success: false,
      errorType: "SERVER_ERROR"
    });
  }
});

router.post("/contact-us", async (req, res) => {
  try {
    const { name, email, message } = req.body;

    const mailOptions = {
      from: process.env.SENDER_EMAIL,
      to: process.env.OWNER_EMAIL,
      subject: "Contact Form Submission",
      text: `${name} has successfully submitted a contact form. Here are the details:
      
- Name: ${name}  
- Email: ${email}  
- Message: ${message}`,
    };

    // Debug log: Verify the recipient and details
    console.log("Mail Options:", mailOptions);

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error("Error sending email:", error);
        return res.status(500).json({
          message: "Error sending email.",
          success: false,
        });
      }
      console.log("Email sent:", info.response);
      return res.json({
        message: "Email sent successfully",
        success: true,
        data: null,
      });
    });
  } catch (error) {
    res.status(500).json({
      message: error.message,
      success: false,
    });
  }
});


// user login

router.post("/login", async (req, res) => {
  try {
    console.log("🔐 Login request body:", req.body);

    // check if user exists by username or email
    const loginField = req.body.email; // This field can contain username or email
    console.log("🔍 Looking for user with:", loginField);

    const user = await User.findOne({
      $or: [
        { email: loginField },
        { username: loginField }
      ]
    });

    console.log("👤 Found user:", user ? "Yes" : "No");

    if (!user) {
      return res
        .status(200)
        .send({ message: "User does not exist", success: false });
    }

    if (user.isBlocked) {
      return res.status(403).send({
        message: "You are blocked. Please contact your moderator",
        success: false,
      });
    }

    // check password
    const validPassword = await bcrypt.compare(
      req.body.password,
      user.password
    );
    if (!validPassword) {
      return res
        .status(200)
        .send({ message: "Invalid password", success: false });
    }

    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || "1d",
    });

    res.send({
      message: "User logged in successfully",
      success: true,
      data: token,
      response: user,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// get all users
router.get("/get-all-users", async (req, res) => {
  try {
    const users = await User.find({ isAdmin: false }); // Filter for non-admin users
    if (users && users.length > 0) {
      const simplifiedUsers = users.map((user) => ({
        studentId: user._id,
        name: user.name,
        school: user.school,
        class: user.class,
        email: user.email,
        profileImage: user.profileImage,
        isAdmin: user.isAdmin,
        isBlocked: user.isBlocked,
        // Add subscription fields for admin filtering
        subscriptionStatus: user.subscriptionStatus,
        subscriptionEndDate: user.subscriptionEndDate,
        subscriptionPlan: user.subscriptionPlan,
        subscriptionStartDate: user.subscriptionStartDate,
        paymentRequired: user.paymentRequired,
        // Add activity tracking fields
        lastActivity: user.lastActivity,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        // Add detailed activity tracking
        activityTracking: user.activityTracking,
        totalQuizzesTaken: user.totalQuizzesTaken,
        lastLoginDate: user.activityTracking?.lastLoginDate,
      }));
      res.send({
        users: simplifiedUsers,
        success: true,
      });
    } else {
      console.error("No User Found");
      res.status(404).send({
        message: "No User Found",
        success: false,
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).send({ message: error.message });
  }
});

// get user info

router.post("/get-user-info", authMiddleware, async (req, res) => {
  try {
    const user = await User.findById(req.body.userId).select(
      "-password -createdAt -updatedAt -__v"
    );
    res.send({
      message: "User info fetched successfully",
      success: true,
      data: user,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// update user info

router.post("/update-user-info", authMiddleware, async (req, res) => {
  const { name, email, school, class_, userId, level, phoneNumber } =
    req.body;

  console.log('📥 Update user info request received:', {
    name, email, school, class_, userId, level, phoneNumber
  });

  try {
    // Validate required fields
    if (!name || name.trim() === "") {
      console.log('❌ Validation failed: Missing name');
      return res.status(400).send({
        message: "Name is required",
        success: false,
      });
    }

    if (!email || email.trim() === "") {
      console.log('❌ Validation failed: Missing email');
      return res.status(400).send({
        message: "Email is required",
        success: false,
      });
    }

    if (!level || level.trim() === "") {
      console.log('❌ Validation failed: Missing level');
      return res.status(400).send({
        message: "Level is required",
        success: false,
      });
    }

    if (!class_ || class_.trim() === "") {
      console.log('❌ Validation failed: Missing class');
      return res.status(400).send({
        message: "Class is required",
        success: false,
      });
    }

    // Get current user data to check for level changes
    const currentUser = await User.findById(userId);
    if (!currentUser) {
      console.log('❌ User not found:', userId);
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Validate level if provided (accept both capitalized and lowercase)
    const validLevels = ["primary", "secondary", "advance", "Primary", "Secondary", "Advance"];
    if (level && !validLevels.includes(level)) {
      console.log('❌ Invalid level provided:', level);
      return res.status(400).send({
        message: "Invalid level. Must be 'Primary', 'Secondary', or 'Advance'",
        success: false,
      });
    }

    // Check if level is changing
    const levelChanged = level && currentUser.level !== level;
    console.log('🔄 Level changed:', levelChanged, 'from', currentUser.level, 'to', level);

    console.log('🔄 Updating user with data:', {
      name, email, school, class: class_, level, phoneNumber
    });

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        name,
        email,
        school,
        class: class_,
        level,
        phoneNumber,
      },
      { new: true }
    );

    if (updatedUser) {
      console.log('✅ User updated successfully:', updatedUser._id);

      const response = {
        message: "User info updated successfully",
        success: true,
        data: updatedUser,
      };

      // Add level change notification if level was changed
      if (levelChanged) {
        response.levelChanged = true;
        response.newLevel = level;
        response.message = `User info updated successfully. Level changed to ${level}. You now have access to ${level} level content only.`;
      }

      res.send(response);
    } else {
      console.log('❌ Failed to update user');
      res.send({
        message: "Unable to update Info",
        success: false,
        data: "error",
      });
    }
  } catch (error) {
    console.error('❌ Error updating user info:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    res.status(500).send({
      message: error.message || "Internal server error",
      data: error,
      success: false,
    });
  }
});

router.post(
  "/update-user-photo",
  upload.single("profileImage"),
  authMiddleware,
  async (req, res) => {
    try {
      const { userId } = req.body;
      const profilePic = req.file;

      var folderName = "Profile";

      // Generate a unique filename with original extension
      const filename = `${folderName}/${uuidv4()}-${profilePic.originalname}`;

      const params = {
        Bucket: process.env.AWS_S3_BUCKET_NAME,
        Key: filename,
        Body: profilePic.buffer,
        ContentType: profilePic.mimetype || "application/octet-stream",
      };

      // Upload image to S3
      const s3Response = await s3.upload(params).promise();

      const imageUrl = s3Response.Location;

      console.log(s3Response, "imageUrl");

      // Update the user's profile image URL in the database
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { profileImage: imageUrl },
        { new: true }
      );

      if (updatedUser) {
        res.send({
          message: "User photo updated successfully",
          success: true,
          data: updatedUser,
        });
      } else {
        res.send({
          message: "Unable to update Photo",
          success: false,
          data: "error",
        });
      }
    } catch (error) {
      console.log(error);
      res.status(500).send({
        message: error.message,
        data: error,
        success: false,
      });
    }
  }
);
// block user
router.patch("/block-user", async (req, res) => {
  try {
    console.log("request :", req.body);
    const { studentId } = req.body;
    if (!studentId) {
      return res.status(400).send({ message: "UserId is not provided" });
    }
    // Find user and check if admin
    const user = await User.findById(studentId); // Use findById to avoid unnecessary update
    if (!user) {
      return res.status(404).send({ message: "User not found" });
    }

    if (user.isAdmin) {
      return res.status(403).send({ message: "Cannot block admin users" });
    }

    const updatedUser = await User.findByIdAndUpdate(
      studentId,
      { isBlocked: !user.isBlocked },
      { new: true }
    );

    if (updatedUser.isBlocked) {
      res.send({
        message: "User is blocked successfully",
        success: true,
      });
    } else {
      res.send({
        message: "User is unblocked successfully",
        success: true,
      });
    }
  } catch (error) {
    console.log(error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// delete user
router.delete("/delete-user", async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { studentId } = req.body;

    if (!studentId) {
      return res.status(400).send({ message: "UserId is not provided" });
    }

    const user = await User.findById(studentId).session(session);
    if (!user) {
      return res.status(404).send({ message: "User not found" });
    }

    if (user.isAdmin) {
      return res.status(403).send({ message: "Cannot delete admin users" });
    }

    await User.findByIdAndDelete(studentId).session(session);

    await Review.deleteMany({ user: user._id }).session(session);
    await Report.deleteMany({ user: user._id }).session(session);
    await forumQuestion.deleteMany({ user: user._id }).session(session);

    await session.commitTransaction();
    session.endSession();

    res.send({
      message: "User deleted successfully, along with all related data",
      success: true,
    });
  } catch (error) {
    // Abort transaction on error
    await session.abortTransaction();
    session.endSession();

    console.log(error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// ===== INACTIVITY PENALTY ROUTES =====

// Manual inactivity check (admin only)
router.post("/check-inactivity-penalties", authMiddleware, async (req, res) => {
  try {
    const user = await User.findById(req.body.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin only.",
        success: false,
      });
    }

    const result = await inactivityPenaltyService.checkInactivityPenalties();

    res.send({
      message: "Inactivity penalties checked successfully",
      success: true,
      data: result,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get inactivity statistics (admin only)
router.get("/inactivity-stats", authMiddleware, async (req, res) => {
  try {
    const user = await User.findById(req.body.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin only.",
        success: false,
      });
    }

    const stats = await inactivityPenaltyService.getInactivityStats();

    res.send({
      message: "Inactivity stats fetched successfully",
      success: true,
      data: stats,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
