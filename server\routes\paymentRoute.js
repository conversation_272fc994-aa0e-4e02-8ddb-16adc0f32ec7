const axios = require("axios");
const qs = require("qs"); // Import qs (for x-www-form-urlencoded)
const router = require("express").Router(); // Import express Router
const moment = require("moment");
const authMiddleware = require("../middlewares/authMiddleware");
const Subscription = require("../models/subscriptionModel");
const User = require("../models/userModel");

const createSubscription = async (user, plan, response) => {
  const paymentDate = new Date();

  try {
    const SubscriptionData = {
      user: user._id,
      activePlan: plan._id,
      paymentStatus: "pending",
      status: "pending",
      paymentHistory: [
        {
          orderId: response.data.order_id,
          plan: plan._id,
          amount: plan.discountedPrice,
          paymentStatus: "pending",
          paymentDate: `${paymentDate.getFullYear()}-${String(
            paymentDate.getMonth() + 1
          ).padStart(2, "0")}-${String(paymentDate.getDate()).padStart(
            2,
            "0"
          )}`, // Set the current date
        },
      ],
    };

    if (response.data.status === "success") {
      const newSubscription = await Subscription.create(SubscriptionData);

      if (newSubscription) {
        return newSubscription; // Return the created subscription
      }
    } else {
      console.log("elc block");
      throw Error("Payment was not successful.");
    }
  } catch (error) {
    console.log(error, "wssss");
    throw error;
  }
};

// POST endpoint for initiating payment
router.post("/create-invoice", authMiddleware, async (req, res) => {
  const url = "https://api.zeno.africa"; // Zeno API base URL

  const { plan } = req.body;
  const userId = req.body.userId; // Get user ID from auth middleware

  console.log('🔍 Debug - Request body:', JSON.stringify(req.body, null, 2));
  console.log('🔍 Debug - Plan data:', plan);
  console.log('🔍 Debug - User ID:', userId);

  const user = await User.findById(userId);
  console.log('🔍 Debug - User found:', user ? `${user.name} (${user.email})` : 'No user found');

  console.log('💳 Payment request received:', { planTitle: plan?.title, userId, userName: user?.name });

  if (!plan) {
    console.log('❌ Validation failed: No plan provided');
    return res.status(400).send({
      message: "Plan data is required.",
      success: false
    });
  }

  if (!user) {
    console.log('❌ Validation failed: User not found');
    return res.status(400).send({
      message: "User not found.",
      success: false
    });
  }

  if (!plan.discountedPrice) {
    console.log('❌ Validation failed: No plan price');
    return res.status(400).send({
      message: "Plan price is required.",
      success: false
    });
  }

  console.log('📱 User phone number:', user.phoneNumber);
  console.log('📧 User email:', user.email);
  console.log('👤 User name:', user.name);
  console.log('🆔 User ID:', user._id);
  console.log('👤 User username:', user.username);

  if (!user.phoneNumber || user.phoneNumber.trim() === "") {
    console.log('❌ Validation failed: User has no phone number');
    return res.status(400).send({
      message: "Phone number is required for payment. Please add your phone number in your profile settings.",
      success: false,
      errorType: "MISSING_PHONE"
    });
  }

  if (!user.name || user.name.trim() === "") {
    console.log('❌ Validation failed: User has no name');
    return res.status(400).send({
      message: "Name is required for payment. Please update your profile.",
      success: false,
      errorType: "MISSING_NAME"
    });
  }

  // Handle email for users who registered with username only
  let buyerEmail = user.email;
  if (!buyerEmail || buyerEmail.trim() === "") {
    // Generate a unique email using user ID (MongoDB ObjectId is always unique)
    const uniqueIdentifier = user._id.toString();
    const timestamp = Date.now();

    // Create unique email: <EMAIL>
    buyerEmail = `user-${uniqueIdentifier}-${timestamp}@brainwave.temp`;
    console.log('📧 Generated unique email for username-only user:', buyerEmail);
  }

  // Data to be sent to Zeno API
  const data = {
    buyer_name: user.name,
    buyer_phone: user.phoneNumber,
    buyer_email: buyerEmail,
    amount: plan.discountedPrice,
    account_id: process.env.ZENOPAY_ACCOUNT_ID,
    secret_key: process.env.ZENOPAY_SECRET_KEY,
    api_key: process.env.ZENOPAY_API_KEY,
    webhook_url: process.env.ZENOPAY_WEBHOOK_URL,
  };

  console.log('📤 Sending payment data to ZenoPay:', {
    buyer_name: data.buyer_name,
    buyer_phone: data.buyer_phone,
    buyer_email: data.buyer_email,
    amount: data.amount,
    account_id: data.account_id ? '***' + data.account_id.slice(-4) : 'NOT_SET',
    webhook_url: data.webhook_url
  });

  console.log('🔍 Full ZenoPay data object:');
  console.log(JSON.stringify(data, null, 2));

  // Additional validation for ZenoPay requirements
  if (!data.buyer_phone || data.buyer_phone.length < 10) {
    console.log('❌ ZenoPay validation: Invalid phone number format');
    return res.status(400).send({
      message: "Please enter a valid 10-digit phone number (e.g., **********).",
      success: false,
      errorType: "INVALID_PHONE_FORMAT"
    });
  }

  if (!data.amount || data.amount <= 0) {
    console.log('❌ ZenoPay validation: Invalid amount');
    return res.status(400).send({
      message: "Invalid payment amount.",
      success: false,
      errorType: "INVALID_AMOUNT"
    });
  }

  // Validate ZenoPay configuration
  if (!process.env.ZENOPAY_ACCOUNT_ID || !process.env.ZENOPAY_SECRET_KEY || !process.env.ZENOPAY_API_KEY) {
    console.error('❌ ZenoPay configuration missing');
    return res.status(500).send({
      message: "Payment service configuration error. Please contact support.",
      success: false,
      errorType: "PAYMENT_CONFIG_ERROR"
    });
  }

  // Convert data to x-www-form-urlencoded format
  const formattedData = qs.stringify(data);

  try {
    console.log('🔄 Sending request to ZenoPay API...');

    // Send POST request to the Zeno API
    const response = await axios.post(url, formattedData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      timeout: 30000 // 30 second timeout
    });

    console.log('📥 ZenoPay API response:', response.data);

    if (response.data.status === "success") {
      console.log('✅ Payment initiated successfully');
      await createSubscription(user, plan, response);

      res.status(200).send({
        ...response.data,
        message: `Payment request sent! Please check your phone for SMS confirmation from ZenoPay.`,
        success: true
      });
    } else {
      console.log('❌ ZenoPay API returned error:', response.data);

      // Handle specific ZenoPay error messages
      let errorMessage = response.data.message || "Payment initiation failed. Please try again.";

      if (errorMessage.includes("Invalid input data")) {
        console.log('🔍 ZenoPay validation error - checking sent data...');
        console.log('📤 Data sent to ZenoPay:', JSON.stringify(data, null, 2));
        console.log('🔍 Checking each field:');
        console.log('  - buyer_name:', data.buyer_name, '(length:', data.buyer_name?.length, ')');
        console.log('  - buyer_phone:', data.buyer_phone, '(length:', data.buyer_phone?.length, ')');
        console.log('  - buyer_email:', data.buyer_email, '(length:', data.buyer_email?.length, ')');
        console.log('  - amount:', data.amount, '(type:', typeof data.amount, ')');
        console.log('  - account_id:', data.account_id, '(length:', data.account_id?.length, ')');
        console.log('  - webhook_url:', data.webhook_url, '(length:', data.webhook_url?.length, ')');

        errorMessage = "Invalid payment data. Please check your profile information and try again.";
      }

      res.status(400).send({
        message: errorMessage,
        success: false,
        errorType: "ZENOPAY_ERROR",
        details: response.data
      });
    }
  } catch (error) {
    console.error("❌ Payment Error:", error.message);
    console.error("Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });

    let errorMessage = "Payment service is currently unavailable. Please try again later.";
    let errorType = "PAYMENT_SERVICE_ERROR";

    if (error.code === 'ECONNREFUSED') {
      errorMessage = "Cannot connect to payment service. Please check your internet connection.";
      errorType = "CONNECTION_ERROR";
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = "Payment request timed out. Please try again.";
      errorType = "TIMEOUT_ERROR";
    } else if (error.response?.status === 401) {
      errorMessage = "Payment service authentication failed. Please contact support.";
      errorType = "AUTH_ERROR";
    } else if (error.response?.data) {
      errorMessage = error.response.data.message || errorMessage;
      errorType = "ZENOPAY_API_ERROR";
    }

    res.status(500).send({
      message: errorMessage,
      success: false,
      errorType: errorType,
      details: error.response?.data || error.message
    });
  }
});

router.get("/check-payment-status", authMiddleware, async (req, res) => {
  const { userId } = req.body;
  try {
    if (!userId) {
      return res.status(400).json({ error: "User ID is required" });
    }
    const currentDate = moment().format("YYYY-MM-DD");
    const subscription = await Subscription.findOne(
      {
        user: userId,
        status: "active",
        paymentStatus: "paid",
        endDate: { $ne: null, $gte: currentDate },
      }
    ).populate("activePlan");

    if (!subscription) {
      return res.status(404).json({ error: "Subscription not found" });
    }

    const lastPayment = subscription.paymentHistory.slice(-1)[0];

    if (!lastPayment) {
      return res.status(404).json({ error: "No payment history found" });
    }

    return res.status(200).json({
      paymentStatus: subscription.paymentStatus,
      amount: lastPayment.amount,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      plan: subscription.activePlan || "No active plan found", // Handle no active plan
    });
  } catch (error) {
    console.error("Error fetching payment status:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

// Webhook handler for payment updates
router.post("/webhook", async (req, res) => {
  try {
    const paymentDate = new Date();
    console.log('Payment Date: ', paymentDate);

    const formattedDate = `${paymentDate.getFullYear()}-${String(
      paymentDate.getMonth() + 1
    ).padStart(2, "0")}-${String(paymentDate.getDate()).padStart(2, "0")}`; // Set the current date

    const data = JSON.parse(req.body.toString());

    console.log("Webhook Data Received: ", data);

    const { order_id, reference, payment_status: status } = data;

    if (!order_id || !reference) {
      throw new Error("Invalid webhook payload");
    }

    const subscription = await Subscription.findOne({
      "paymentHistory.orderId": order_id,
    }).populate("paymentHistory.plan");

    if (!subscription) {
      throw new Error(`No subscription found with orderId: ${order_id}`);
    }

    const paymentHistoryIndex = subscription.paymentHistory.findIndex(
      (payment) => payment.orderId === order_id
    );

    if (paymentHistoryIndex === -1) {
      throw new Error(`Payment history not found for orderId: ${order_id}`);
    }

    subscription.paymentHistory[paymentHistoryIndex].referenceId = reference;
    subscription.paymentHistory[paymentHistoryIndex].paymentDate =
      formattedDate;

    if (status === "COMPLETED") {
      const endDate = paymentDate;
      endDate.setMonth(endDate.getMonth() + subscription.paymentHistory[paymentHistoryIndex].plan.duration);
      const formattedEndDate = `${endDate.getFullYear()}-${String(
        endDate.getMonth() + 1
      ).padStart(2, "0")}-${String(endDate.getDate()).padStart(2, "0")}`;
      subscription.paymentHistory[paymentHistoryIndex].paymentStatus = "paid";
      subscription.paymentStatus = "paid";
      subscription.startDate = formattedDate;
      subscription.endDate = formattedEndDate;
      subscription.status = "active";
    } else {
      subscription.paymentHistory[paymentHistoryIndex].paymentStatus = "failed";
      subscription.paymentStatus = "failed";
      subscription.status = "expired";
    }

    await subscription.save();

    console.log("Subscription updated successfully:", subscription);

    res.status(200).send("Webhook received successfully");
  } catch (error) {
    console.error("Webhook Error:", error.message);
    res.status(500).send("Webhook processing failed");
  }
});

module.exports = router;