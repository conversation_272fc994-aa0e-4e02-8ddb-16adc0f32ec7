import React, { useEffect, useState } from "react";
import <PERSON><PERSON> from "react-modal";
import "./ConfirmationModal.css";

Modal.setAppElement("#root"); // Ensure accessibility for screen readers

const ConfirmModal = ({ isOpen, onClose, transaction }) => {
    const [showConfetti, setShow<PERSON>onfetti] = useState(false);

    useEffect(() => {
        if (isOpen) {
            // Trigger confetti animation
            setShowConfetti(true);
            setTimeout(() => setShowConfetti(false), 3000);

            // Play success sound using Web Audio API
            try {
                // Create a simple success sound using Web Audio API
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // Success sound: ascending notes
                oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
                oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
                oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2); // G5

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
            } catch (error) {
                console.log('Audio not available');
            }
        }
    }, [isOpen]);

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            className="modal-content success-modal"
            overlayClassName="modal-overlay"
        >
            {showConfetti && (
                <div className="confetti-container">
                    {[...Array(50)].map((_, i) => (
                        <div key={i} className={`confetti confetti-${i % 5}`}></div>
                    ))}
                </div>
            )}

            <div className="modal-header">
                <div className="success-icon-container">
                    <div className="success-checkmark">
                        <svg width="80px" height="80px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="10" fill="#10B981" className="check-circle"/>
                            <path d="M8 12l2 2 4-4" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="check-mark"/>
                        </svg>
                    </div>
                    <div className="success-glow"></div>
                </div>
            </div>

            <h2 className="modal-title">🎉 Payment Successful!</h2>
            <p className="modal-subtitle">Your subscription has been activated successfully</p>
            <div className="success-features">
                <div className="feature-item">
                    <div className="feature-icon">📚</div>
                    <div className="feature-text">
                        <h4>Full Access Unlocked</h4>
                        <p>Access all quizzes and study materials</p>
                    </div>
                </div>
                <div className="feature-item">
                    <div className="feature-icon">🎯</div>
                    <div className="feature-text">
                        <h4>Progress Tracking</h4>
                        <p>Monitor your learning journey</p>
                    </div>
                </div>
                <div className="feature-item">
                    <div className="feature-icon">🏆</div>
                    <div className="feature-text">
                        <h4>Achievement System</h4>
                        <p>Earn badges and certificates</p>
                    </div>
                </div>
            </div>

            <div className="modal-details">
                <div className="detail-item">
                    <span className="detail-label">SUBSCRIPTION</span>
                    <span className="detail-value">Premium Access</span>
                </div>
                <div className="detail-item">
                    <span className="detail-label">AMOUNT PAID</span>
                    <span className="detail-value">{transaction?.amount || 'N/A'} {transaction?.amount !== 'N/A' ? 'TZS' : ''}</span>
                </div>
                <div className="detail-item">
                    <span className="detail-label">STATUS</span>
                    <span className="detail-value success-status">✅ Active</span>
                </div>
                <div className="detail-item">
                    <span className="detail-label">WELCOME MESSAGE</span>
                    <span className="detail-value">Welcome to BrainWave Premium! 🚀</span>
                </div>
            </div>

            <button className="modal-button success-button" onClick={onClose}>
                🎉 Start Learning Now!
            </button>
        </Modal>
    );
};

export default ConfirmModal;
