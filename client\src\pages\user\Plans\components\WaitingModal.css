/* Modal Overlay */
.waiting-modal-overlay {
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Modal Content */
.waiting-modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 32px;
    max-width: 480px;
    width: 100%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    overflow: none;
    border: none;
    position: relative;
}

/* Payment Icon Container */
.payment-icon-container {
    margin-bottom: 24px;
}

.payment-processing-icon {
    position: relative;
    display: inline-block;
}

.payment-icon {
    animation: pulse 2s infinite;
}

.loading-spinner {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 100px;
    height: 100px;
    border: 3px solid #e3f2fd;
    border-top: 3px solid #007BFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Modal Header */
.waiting-modal-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a365d;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #007BFF, #0056D2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.payment-subtitle {
    font-size: 1rem;
    color: #64748b;
    margin-bottom: 24px;
    font-weight: 500;
}

/* Progress Section */
.waiting-modal-timer {
    margin: 24px 0;
}

.progress-container {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007BFF, #0056D2);
    border-radius: 4px;
    animation: progress 3s ease-in-out infinite;
}

.progress-text {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

/* Payment Instructions */
.payment-instructions {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin: 24px 0;
    text-align: left;
}

.instruction-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    last-child: {
        margin-bottom: 0;
    }
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #007BFF, #0056D2);
    color: white;
    border-radius: 50%;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 12px;
    flex-shrink: 0;
}

.step-text {
    font-size: 0.95rem;
    color: #374151;
    font-weight: 500;
}

/* Security Notice */
.security-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 8px;
    padding: 12px;
    margin-top: 20px;
}

.security-notice span {
    font-size: 0.85rem;
    color: #166534;
    font-weight: 500;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Responsive Design */
@media (max-width: 480px) {
    .waiting-modal-content {
        margin: 20px;
        padding: 24px;
        max-width: calc(100vw - 40px);
    }

    .waiting-modal-header h2 {
        font-size: 1.5rem;
    }

    .loading-spinner {
        width: 80px;
        height: 80px;
        top: -8px;
        left: -8px;
    }

    .payment-icon {
        width: 64px;
        height: 64px;
    }
}

.waiting-modal-timer svg {
    margin-bottom: 12px;
    animation: spin 2s linear infinite; /* Add spinning animation */
}

/* Spinning animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Footer Text */
.waiting-modal-footer {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 16px;
}
